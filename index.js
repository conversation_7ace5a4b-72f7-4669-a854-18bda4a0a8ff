const http = require('http');
const express = require('express');
const app = express();
const connectDB = require('./config/db');
require('dotenv').config();

app.use(express.json());

const port = process.env.PORT || 3000;
const server = http.createServer(app);

connectDB();

app.get('/', (req, res) => {
    res.send({
        message: 'Hello World!'
    })
});

server.listen(port, () => {
    console.log(`Server started on port ${port}`);
});